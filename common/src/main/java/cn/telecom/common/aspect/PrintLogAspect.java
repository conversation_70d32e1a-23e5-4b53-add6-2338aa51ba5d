package cn.telecom.common.aspect;


import cn.telecom.common.annotation.PrintLog;
import cn.telecom.common.factory.NameThreadFactory;
import cn.telecom.common.model.PrintLogStrategy;
import cn.telecom.common.util.LoggerUtil;
import com.alibaba.fastjson.JSONObject;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Component
@Aspect
public class PrintLogAspect {
    @Value("${custom.log-thread-num}")
    private int threadNum;

    private ExecutorService executors;

    @PostConstruct
    public void initThreadPool(){
        executors =  Executors.newFixedThreadPool( threadNum,new NameThreadFactory("log-print-"));
    }

    @Pointcut("@annotation(cn.telecom.common.annotation.PrintLog)")
    public void pointcut() {
    }

    @Around("pointcut()")
    public Object doInvoke(ProceedingJoinPoint pjp) {
        long start = System.currentTimeMillis();
        Object result = null;
        try {
            result = pjp.proceed();
        } catch (Throwable throwable) {
            LoggerUtil.error(throwable.getMessage(), throwable);
            result = "系统异常";
        } finally {
            long end = System.currentTimeMillis();
            long elapsedTime = end - start;
            final Object finalResult = result;
            executors.submit(() -> printLog(pjp, finalResult, elapsedTime));
        }

        return result;
    }

    /**
     * 打印日志
     * @param pjp   连接点
     * @param result    方法调用返回结果
     * @param elapsedTime   方法调用花费时间
     */
    private void printLog(ProceedingJoinPoint pjp, Object result, long elapsedTime) {
        PrintLogStrategy strategy = getFocus(pjp);
        if (null != strategy) {
            PrintLog annation = getAnnotation(pjp);
            if(!annation.ignoreResp()){
                strategy.setResult(JSONObject.toJSONString(result));
            }
            strategy.setElapsedTime(elapsedTime);
            LoggerUtil.info(strategy.format(), strategy.args());
        }
    }

    /**
     * 获取注解
     */
    private PrintLogStrategy getFocus(ProceedingJoinPoint pjp) {
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        String className = signature.getDeclaringTypeName();
        Method method = signature.getMethod();
        String methodName = signature.getName();
        Object[] args = pjp.getArgs();
        try {
            PrintLogStrategy strategy = new PrintLogStrategy();
            strategy.setClassName(className);
            strategy.setMethodName(methodName);

            PrintLog printLog = method.getAnnotation(PrintLog.class);
            if (null != printLog) {
                strategy.setArguments(JSONObject.toJSONString(args));
                strategy.setDescription(printLog.description());
                return strategy;
            }
        } catch (Exception e) {
            LoggerUtil.error(e.getMessage(), e);
        }
        return null;
    }

    private PrintLog getAnnotation(ProceedingJoinPoint pjp){
        MethodSignature methodSignature = (MethodSignature) pjp.getSignature();
        return methodSignature.getMethod().getAnnotation(PrintLog.class);
    }
}
