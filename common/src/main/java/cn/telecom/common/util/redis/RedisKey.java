package cn.telecom.common.util.redis;

import cn.hutool.core.util.StrUtil;

public class RedisKey {
    public static final String APP = "app";
    public static final String BIND = "bind";
    public static final String NUMBER = "number";
    public static final String TEMP_BIND = "tempbind";
    public static final String LOCK_EXPIRE_CHECK = "expire-check";
    public static final String CALL_DETAIL_QUEUE = "calldetail-queue";

    public static String getBindKey(String appId,String number){
        return StrUtil.join(":",BIND,appId,number);
    }

    public static String getTempBindKey(String number){
        return StrUtil.join(":",TEMP_BIND,number);
    }

    public static String getNumberKey(String appId){
        return StrUtil.join(":",NUMBER,appId);
    }

    public static String getKey(Object ... keys){
        return StrUtil.join(":",keys);
    }
}
