package cn.telecom.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.telecom.common.model.NumberBind;
import cn.telecom.common.util.redis.RedisKey;
import cn.telecom.common.util.redis.RedisUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BindUtil {
    public static void put(NumberBind bind, RedisUtil redisUtil){
        if(CollUtil.isEmpty(bind.getNumberAList())) return;

        Map<String,String> xmap = new HashMap<>(5);
        //String[] arrA = bind.getNumberA().split(",");

        String xmapVal = StrUtil.join("-",bind.getNumberB(),bind.getNumberY(),bind.getBizId(),"0",bind.getSysCode());

        for(String numberA : bind.getNumberAList()){
            xmap.put(StrUtil.join("-",numberA,bind.getNumberX()),xmapVal);
        }
        //bmap.put(bxKey,StrUtil.join("-",arrA[0],bind.getNumberY(),bind.getBizId()));

        //redisUtil.clearMap(RedisKey.getBindKey(bind.getAppId(),bind.getNumberX()),bmap);
        redisUtil.clearMap(RedisKey.getBindKey(bind.getAppId(),bind.getNumberX()),xmap);
        handleYmap(bind,bind.getNumberAList(),redisUtil);
    }

    public static void handleYmap(NumberBind bind, List<String> numberAList, RedisUtil redisUtil){
        String key = RedisKey.getBindKey(bind.getAppId(),bind.getNumberY());
        String mkey =  StrUtil.join("-",bind.getNumberB(),bind.getNumberY());
        String mval = (String)redisUtil.hget(key,mkey);
        String numberA = numberAList.get(0);
        if(StrUtil.isNotEmpty(mval)){
            String[] valArr = mval.split("-");
            if(numberAList.contains(valArr[0]) && bind.getNumberX().equals(valArr[1]) && bind.getBizId().equals(valArr[2])){
                return;
            }
        }
        redisUtil.hset(key,mkey,StrUtil.join("-",numberA,bind.getNumberX(),bind.getBizId(),"1",bind.getSysCode()));
    }

    public static void remove(NumberBind bind,RedisUtil redisUtil){
        redisUtil.hdel(RedisKey.getBindKey(bind.getAppId(),bind.getNumberY()),StrUtil.join("-",bind.getNumberB(),bind.getNumberY()));
        redisUtil.del(RedisKey.getBindKey(bind.getAppId(),bind.getNumberX()));
    }
}
