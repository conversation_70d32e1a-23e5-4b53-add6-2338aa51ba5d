

package cn.telecom.common.util;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LoggerUtil {

    /**
     * 私有构造 Creates a new instance of LoggerUtil.
     *
     */
    private LoggerUtil() {

    }

    /**
     * 当前对象的路径
     */
    private static String thisClassUrl = LoggerUtil.class.getName();


    private static Logger getLog() {
        Logger logger = LoggerFactory.getLogger(getClazzName());
        return logger;
    }

    /**
     * 获取类名
     *
     * @return
     * <AUTHOR> Lai
     * @created 2018-9-7 下午5:45:53
     */
    private static String getClazzName() {
        StackTraceElement[] ste = Thread.currentThread().getStackTrace();
        String className = null;
        // 从栈的最上开始 往下找 找到第一个不为Log和线程的类
        for (int i = 0; i < ste.length; i++) {
            className = ste[i].getClassName();

            if ("java.lang.Thread".equals(className) || thisClassUrl.equals(className)) {
                if (i != (ste.length - 1)) {
                    // 如果不是最后一个就跳过，如果是最后一个那就没办法了，返回这栈信息吧
                    continue;
                }
            }
            break;
        }
        return className;
    }

    public static void debug(String arg0, Object ... arg1) {
        Logger log = getLog();
        if (log.isDebugEnabled()) {
            log.debug(arg0, arg1);
        }
    }

    public static void error(String arg0, Object ... arg1) {
        Logger log = getLog();
        if (log.isErrorEnabled()) {
            log.error(arg0, arg1);
        }
    }

    public static void info(String arg0, Object ... arg1) {
        Logger log = getLog();
        if (log.isInfoEnabled()) {
            log.info(arg0, arg1);
        }
    }

    public static void trace(String arg0, Object ... arg1) {
        Logger log = getLog();
        if (log.isTraceEnabled()) {
            log.trace(arg0, arg1);
        }
    }

    public static void warn(String arg0, Object ... arg1) {
        Logger log = getLog();
        if (log.isWarnEnabled()) {
            log.warn(arg0, arg1);
        }
    }

}

