package cn.telecom.common.util.redis;

import cn.hutool.core.util.RandomUtil;

public class RedisLock {
    private static final String LOCKED = "TRUE";
    private static final long DEFAULT_TIME_OUT = 180;
    private String key;
    private RedisUtil  redisUtil;

    public RedisLock(String key, RedisUtil redisUtil) {
        this.key = "lock_" + key;
        this.redisUtil = redisUtil;
    }

    public boolean lock() {
        return this.lock(DEFAULT_TIME_OUT);
    }

    public boolean lock(long timeout) {
        return redisUtil.setIfAbsent(key,LOCKED,timeout);
    }

    /**
     * 等待锁，知道成功后返回
     */
    public void waitLock() {
        try {
            while (!lock()){
                //增加休眠时间，防止redis访问频率过大
                Thread.sleep(RandomUtil.randomInt(500));
            }
        } catch (Exception e) {
            throw new RuntimeException("Locking error", e);
        }
    }

    /**
     * 等待锁，知道成功后返回
     */
    public void waitLock(long timeout) {
        try {
            while (!lock(timeout)){
                //增加休眠时间，防止redis访问频率过大
                Thread.sleep(RandomUtil.randomInt(500));
            }
        } catch (Exception e) {
            throw new RuntimeException("Locking error", e);
        }
    }

    public void unlock() {
        redisUtil.del(key);
    }

    public String getKey() {
        return this.key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}
