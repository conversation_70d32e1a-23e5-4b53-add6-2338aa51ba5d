package cn.telecom.common.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 虚号码绑定关系Model
 */
public class NumberBind implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer id;
    private String appId;
    private String numberB;
    private String numberX;
    private String numberY;
    private Date expireTime;
    private String bizId;
    private String sysCode;
    List<String> numberAList;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNumberB() {
        return numberB;
    }

    public void setNumberB(String numberB) {
        this.numberB = numberB;
    }

    public String getNumberX() {
        return numberX;
    }

    public void setNumberX(String numberX) {
        this.numberX = numberX;
    }

    public String getNumberY() {
        return numberY;
    }

    public void setNumberY(String numberY) {
        this.numberY = numberY;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getSysCode() {
        return sysCode;
    }

    public void setSysCode(String sysCode) {
        this.sysCode = sysCode;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public List<String> getNumberAList() {
        return numberAList;
    }

    public void setNumberAList(List<String> numberAList) {
        this.numberAList = numberAList;
    }
}
