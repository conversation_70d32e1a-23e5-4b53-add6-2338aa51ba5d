package cn.telecom.common.model;

import java.io.Serializable;
import java.util.Date;

public class CallDetail implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    private String appId;
    private String sessionid;
    private String displayNbr;
    private String callerNbr;
    private String calledNbr;
    private String serviceNbr;
    private String serviceNbrB;
    private Date dialTime;
    private Date ringTime;
    private Date startTime;
    private Date endTime;
    private String duration;
    private String minDuration;
    private String serviceType;
    private String disposition;
    private String voiceAddress;
    private String voiceAddressIn;
    private String voiceAddressOut;
    private String md5Value;
    private String md5ValueIn;
    private String md5ValueOut;
    private String extensionNum;
    private String userData;
    private String sysCode;
    private String empId;
    private String callType;
    private Date createTime;
    private String sfVoiceAddress;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSessionid() {
        return sessionid;
    }

    public void setSessionid(String sessionid) {
        this.sessionid = sessionid;
    }

    public String getDisplayNbr() {
        return displayNbr;
    }

    public void setDisplayNbr(String displayNbr) {
        this.displayNbr = displayNbr;
    }

    public String getCallerNbr() {
        return callerNbr;
    }

    public void setCallerNbr(String callerNbr) {
        this.callerNbr = callerNbr;
    }

    public String getCalledNbr() {
        return calledNbr;
    }

    public void setCalledNbr(String calledNbr) {
        this.calledNbr = calledNbr;
    }

    public String getServiceNbr() {
        return serviceNbr;
    }

    public void setServiceNbr(String serviceNbr) {
        this.serviceNbr = serviceNbr;
    }

    public String getServiceNbrB() {
        return serviceNbrB;
    }

    public void setServiceNbrB(String serviceNbrB) {
        this.serviceNbrB = serviceNbrB;
    }

    public Date getDialTime() {
        return dialTime;
    }

    public void setDialTime(Date dialTime) {
        this.dialTime = dialTime;
    }

    public Date getRingTime() {
        return ringTime;
    }

    public void setRingTime(Date ringTime) {
        this.ringTime = ringTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public String getMinDuration() {
        return minDuration;
    }

    public void setMinDuration(String minDuration) {
        this.minDuration = minDuration;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getDisposition() {
        return disposition;
    }

    public void setDisposition(String disposition) {
        this.disposition = disposition;
    }

    public String getVoiceAddress() {
        return voiceAddress;
    }

    public void setVoiceAddress(String voiceAddress) {
        this.voiceAddress = voiceAddress;
    }

    public String getVoiceAddressIn() {
        return voiceAddressIn;
    }

    public void setVoiceAddressIn(String voiceAddressIn) {
        this.voiceAddressIn = voiceAddressIn;
    }

    public String getVoiceAddressOut() {
        return voiceAddressOut;
    }

    public void setVoiceAddressOut(String voiceAddressOut) {
        this.voiceAddressOut = voiceAddressOut;
    }

    public String getMd5Value() {
        return md5Value;
    }

    public void setMd5Value(String md5Value) {
        this.md5Value = md5Value;
    }

    public String getMd5ValueIn() {
        return md5ValueIn;
    }

    public void setMd5ValueIn(String md5ValueIn) {
        this.md5ValueIn = md5ValueIn;
    }

    public String getMd5ValueOut() {
        return md5ValueOut;
    }

    public void setMd5ValueOut(String md5ValueOut) {
        this.md5ValueOut = md5ValueOut;
    }

    public String getExtensionNum() {
        return extensionNum;
    }

    public void setExtensionNum(String extensionNum) {
        this.extensionNum = extensionNum;
    }

    public String getUserData() {
        return userData;
    }

    public void setUserData(String userData) {
        this.userData = userData;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getSysCode() {
        return sysCode;
    }

    public void setSysCode(String sysCode) {
        this.sysCode = sysCode;
    }

    public String getEmpId() {
        return empId;
    }

    public void setEmpId(String empId) {
        this.empId = empId;
    }

    public String getCallType() {
        return callType;
    }

    public void setCallType(String callType) {
        this.callType = callType;
    }

    public String getSfVoiceAddress() {
        return sfVoiceAddress;
    }

    public void setSfVoiceAddress(String sfVoiceAddress) {
        this.sfVoiceAddress = sfVoiceAddress;
    }
}
