<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>120-vno</artifactId>
        <groupId>cn.telecome</groupId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <description>顺丰适配模块</description>
    <artifactId>adapter-120</artifactId>

    <dependencies>
        <dependency>
            <groupId>cn.telecome</groupId>
            <artifactId>common</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>javax.mail-api</artifactId>
            <version>1.6.2</version>
        </dependency>
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
            <version>1.6.2</version>
        </dependency>
<!--        &lt;!&ndash;  mybatis分页依赖  &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.github.pagehelper</groupId>-->
<!--            <artifactId>pagehelper-spring-boot-starter</artifactId>-->
<!--            <version>1.2.12</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>1.27</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.15</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <!-- 打包成jar文件，并指定lib文件夹以及resources资源文件夹 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <!--运行jar包时运行的主类，要求类全名-->
                            <mainClass>cn.telecom.adapter.sf.AdapterSfStarter</mainClass>
                            <!-- 指定依赖的时候声明前缀 -->
                            <classpathPrefix>lib/</classpathPrefix>
                            <!-- 是否指定项目classpath下的依赖 -->
                            <addClasspath>true</addClasspath>
                        </manifest>
                        <manifestEntries>
                            <Class-Path>/</Class-Path>
                        </manifestEntries>
                    </archive>
                    <excludes>
                        <!-- 去掉不需要打进 jar 的配置文件 -->
                        <exclude>**/*.yml</exclude>
                        <exclude>**/*.sh</exclude>
                        <exclude>**/bin</exclude>
                        <exclude>**/config</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <!--maven-assembly-plugin，负责将整个项目按照自定义的目录结构打成最终的压缩包，方便实际部署 -->
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <appendAssemblyId>false</appendAssemblyId>
                    <outputDirectory>${project.build.directory}/dist/</outputDirectory>
                    <descriptors>
                        <descriptor>src/main/resources/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
