#!/bin/sh
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
filepwd=`ls $DIR/*.jar`
project=${filepwd##*/}

pid=`ps -ef |grep java |grep $project |awk '{print $2}'`
if [[ -n $pid ]];then
    echo "kill -9 " $pid
    kill -9 $pid
else
    echo "$project is not running"
fi
# 如果使用skywalking 需要增加-javaagent:/home/<USER>/skywalking-agent/skywalking-agent.jar -Dskywalking.agent.service_name=gateway 这一段
nohup java -Dloader.path="$DIR/lib/" -jar $DIR/$project  > nohup.out  2>&1 &
echo "$project has been restarting"
tail -f nohup.out

