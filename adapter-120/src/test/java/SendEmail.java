import javax.mail.*;
import javax.mail.internet.*;
import java.util.Date;
import java.util.Properties;
import java.util.Calendar;

public class SendEmail {
    public static void main(String[] args) {
        // 收件人电子邮件地址
        String to = "<EMAIL>";

        // 发件人电子邮件地址
        String from = "<EMAIL>";

        // 邮件服务器地址（SMTP服务器）
        String host = "smtp.163.com";

        // 设置系统属性
        Properties properties = System.getProperties();

        // 设置邮件服务器
        properties.setProperty("mail.smtp.host", host);
        properties.setProperty("mail.smtp.port", "25"); // 默认端口
        properties.setProperty("mail.smtp.auth", "true"); // 需要身份验证
        properties.setProperty("mail.smtp.starttls.enable", "true"); // 启用TLS加密

        // 获取默认session对象
        Session session = Session.getDefaultInstance(properties, new Authenticator() {
            // 身份验证
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication("<EMAIL>", "UHBKIETOVTTJKMZP");
            }
        });

        try {
            // 创建默认的 MimeMessage 对象
            MimeMessage message = new MimeMessage(session);

            // 设置发件人
            message.setFrom(new InternetAddress(from));

            // 设置收件人
            message.addRecipient(Message.RecipientType.TO, new InternetAddress(to));

            // 设置邮件主题
            message.setSubject("This is the Subject Line!");

            // 设置邮件内容
            message.setText("This is the actual message");

            // 设置发送时间为 2023 年
            Calendar calendar = Calendar.getInstance();
            calendar.set(2023, Calendar.SEPTEMBER, 1, 10, 0, 0); // 2023年9月1日 10:00:00
            Date date = calendar.getTime();

            // 设置发送时间
            message.setSentDate(date);

            // 强制覆盖 Date 头
            message.addHeader("Date", "Fri, 1 Sep 2023 10:00:00 +0800");

            // 发送消息
            Transport.send(message);
            System.out.println("Sent message successfully....");
        } catch (MessagingException mex) {
            mex.printStackTrace();
        }
    }
}
