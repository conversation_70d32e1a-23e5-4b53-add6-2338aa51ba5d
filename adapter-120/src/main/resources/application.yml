server:
  servlet:
    context-path: /adapter-120
  #服务端口
  port: 30000
  tomcat:
    uri-encoding: utf-8
    threads:
      max: 1000
      min-spare: 30

spring:
  application:
    name: adapter-120
  mvc:
    throw-exception-if-no-handler-found: true
    #将默认的favicon.ioc关闭
    favicon:
      enabled: false
  devtools:
    restart:
      #热部署生效 true:非静态代码更改时重新启动服务器; false:禁用非静态代码更改时重新启动服务器
      enabled: true
  main:
    # 启动/关闭 (CONSOLE/OFF)springboot启动时的banner
    banner-mode: CONSOLE
  profiles:
    active: dev

logging:
  config: classpath:logback-spring.xml
#  path: /Users/<USER>/temp/logs/adapter-sf
  path: /webapps/logs/adapter-sf

#########################################
#
##Mybatis配置,官方参考：
#
#########################################
mybatis:
  configuration:
    # 开启驼峰格式
    map-underscore-to-camel-case: true
  # 所有的mapper映射文件
  mapper-locations: classpath:mapper/*Mapper.xml


#########################################
#
#pagehelper 分页插件
#
#########################################
pagehelper:
  #指定数据库 (可以不配置，插件会自动检测数据库的类型)
  helper-dialect: mysql
  #分页合理化参数，默认值为false (当该参数设置为 true 时，pageNum<=0 时会查询第一页， pageNum>pages（超过总数时），会查询最后一页。默认false 时，直接根据参数进行查询)
  reasonable: true
  #默认值false (分页插件会从查询方法的参数值中，自动根据上面 params 配置的字段中取值，查找到合适的值时就会自动分页。)
  support-methods-arguments: true
  #用于从对象中根据属性名取值(可以配置pageNum,pageSize,count,pageSizeZero,reasonable。不配置映射的用默认值)
  params: count=countsql


encryption:
  key: 5TA2kWCuKNp0jbvMrTaCXg==
  iv: 3XwI/ETX3OP5H+R3VHz4lg==
app:
  credentials:
    appId: 123456
    appSecret: 1234567
