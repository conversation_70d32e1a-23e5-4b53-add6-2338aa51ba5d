#########################################
#
#datasource配置、Druid数据库连接池配置、Druid StatViewServlet监控配置
#
#
#########################################
spring:
  datasource:
    # 数据库驱动配置
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************
    username: sh_dict_yqwh
    password: hx_dict_yqwh
    #Druid数据库连接池和监控
    druid:
      initial-size: 5
      min-idle: 3
      max-active: 30
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 30000
      validation-query: SELECT 1 FROM DUAL #select 'x',
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 合并多个DruidDataSource的监控数据
      use-global-data-source-stat: true
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

      # 配置过滤规则
      # Druid WebStatFilter配置，说明请参考Druid Wiki，配置_配置WebStatFilter
      web-stat-filter:
        enabled: true
        # 添加过滤规则
        url-pattern: /*
        # 忽略过滤格式
        exclusions: "*.gif,*.png,*.jpg,*.html,*.js,*.css,*.ico,/druid/*"

      # 配置监控服务器
      # Druid StatViewServlet配置，说明请参考Druid Wiki，配置_StatViewServlet配置
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: true
        login-username: reaper
        login-password: reaper
        # 添加IP白名单
        allow: "127.0.0.1"
        # 添加IP黑名单，当白名单和黑名单重复时，黑名单优先级更高
        deny: ""
  #redis参数配置
  redis:
    database: 0
    host: **************
    port: 26379
    timeout: 3000
    password: ideal
    lettuce:
      pool:
        max-active: 10
        max-idle: 5
        min-idle: 1
        max-wait: 3000


#自定义配置
custom:
  log-thread-num: 5

