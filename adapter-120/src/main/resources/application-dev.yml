#########################################
#
#datasource配置、Druid数据库连接池配置、Druid StatViewServlet监控配置
#
#
#########################################
spring:
  web:
    resources:
      static-locations: classpath:/META-INF/resources/, classpath:/resources/, classpath:/static/, classpath:/public/
  redis:
    database: 0
    host: 127.0.0.1
    port: 26379
    timeout: 3000
    password: ideal
    lettuce:
      pool:
        max-active: 10
        max-idle: 5
        min-idle: 1
        max-wait: 3000

#自定义配置
custom:
  log-thread-num: 1
  appId: 96b39c831a30535f27a9b69157c480a3
  appKey: 156529737e4976584e7892852ab1a736
#  customerUrl: http://*************:7822/openApi/crmcus/customerPosition/customerPosition
#  addressUrl: http://*************:7822/openApi/crmcus/prodInst/prodInst
  customerUrl: https://**************:18443/openApi/omsop/customerPosition/proxy/api/gateway/proxy/api/activity/shanghai/customerPosition
  addressUrl: https://**************:18443/openApi/crmcus/prodInstHttps/prodInst




