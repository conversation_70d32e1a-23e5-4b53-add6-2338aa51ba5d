package cn.telecom.adapter.sf.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

@Configuration
public class SecurityConfig {

    @Value("${encryption.key}")
    private String encryptionKey;

    @Value("${encryption.iv}")
    private String encryptionIv;

    @Bean
    public SecretKeySpec secretKeySpec() {
        return new SecretKeySpec(Base64.getDecoder().decode(encryptionKey), "AES");
    }

    @Bean
    public IvParameterSpec ivParameterSpec() {
        return new IvParameterSpec(Base64.getDecoder().decode(encryptionIv));
    }
}
