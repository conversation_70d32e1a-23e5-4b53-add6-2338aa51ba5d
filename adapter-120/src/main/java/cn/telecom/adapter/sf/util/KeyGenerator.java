package cn.telecom.adapter.sf.util;

import java.security.SecureRandom;
import java.util.Base64;

public class KeyGenerator {

    public static void main(String[] args) {
        int keyLength = 16;

        // 生成随机密钥
        byte[] secretKeyBytes = new byte[keyLength];
        SecureRandom secureRandom = new SecureRandom();
        secureRandom.nextBytes(secretKeyBytes);

        // 将字节数组编码为 Base64 字符串
        String secretKeyBase64 = Base64.getEncoder().encodeToString(secretKeyBytes);
        System.out.println("Your secret key (Base64 encoded): " + secretKeyBase64);

        // 生成随机初始化向量 (IV)
        byte[] ivBytes = new byte[keyLength];
        secureRandom.nextBytes(ivBytes);

        // 将字节数组编码为 Base64 字符串
        String ivBase64 = Base64.getEncoder().encodeToString(ivBytes);
        System.out.println("Your IV (Base64 encoded): " + ivBase64);
    }
}
