package cn.telecom.adapter.sf.interceptor;

import cn.telecom.adapter.sf.service.impl.RateLimitService;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Component
public class RateLimitInterceptor implements HandlerInterceptor {

    private final RateLimitService rl;

    public RateLimitInterceptor(RateLimitService rl) { this.rl = rl; }

    @Override
    public boolean preHandle(HttpServletRequest req, HttpServletResponse resp, Object h) throws IOException {
        String token = req.getHeader("Authorization");
        if (!rl.allow(token)) {
            resp.sendError(HttpStatus.TOO_MANY_REQUESTS.value(), "Rate limit exceeded");
            return false;
        }
        return true;
    }
}
