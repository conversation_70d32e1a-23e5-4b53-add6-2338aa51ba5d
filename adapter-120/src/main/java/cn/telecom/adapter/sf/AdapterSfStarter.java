package cn.telecom.adapter.sf;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(scanBasePackages = {"cn.telecom"} ,exclude = DataSourceAutoConfiguration.class)
public class AdapterSfStarter {
	 public static void main(String[] args) {
	      SpringApplication.run(AdapterSfStarter.class, args);
	    }
}
