package cn.telecom.adapter.sf.model.req;

public class CallDetailReq extends BaseReq {

    private String pullType;
    private String billType;
    private Integer recordCount;
    private String startTime;
    private String endTime;
    private Integer startIndex;
    private String transId;
    private String appId;
    private String statusCode;
    private String status;


    private String callstartTimeStart;

    private String callstartTimeEnd;

    public String getPullType() {
        return pullType;
    }

    public void setPullType(String pullType) {
        this.pullType = pullType;
    }

    public String getBillType() {
        return billType;
    }

    public void setBillType(String billType) {
        this.billType = billType;
    }


    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getRecordCount() {
        return recordCount;
    }

    public void setRecordCount(Integer recordCount) {
        this.recordCount = recordCount;
    }

    public Integer getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(Integer startIndex) {
        this.startIndex = startIndex;
    }

    public String getTransId() {
        return transId;
    }

    public void setTransId(String transId) {
        this.transId = transId;
    }


    public String getCallstartTimeStart() {
        return callstartTimeStart;
    }

    public void setCallstartTimeStart(String callstartTimeStart) {
        this.callstartTimeStart = callstartTimeStart;
    }

    public String getCallstartTimeEnd() {
        return callstartTimeEnd;
    }

    public void setCallstartTimeEnd(String callstartTimeEnd) {
        this.callstartTimeEnd = callstartTimeEnd;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(String statusCode) {
        this.statusCode = statusCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
