package cn.telecom.adapter.sf.service.impl;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Service
public class RateLimitService {

    private static final int LIMIT = 60;                     // 每分钟 60 次
    private static final Duration WINDOW = Duration.ofMinutes(1);

    private final StringRedisTemplate redis;                 // Spring Data Redis

    public RateLimitService(StringRedisTemplate redis) {
        this.redis = redis;
    }

    /**
     * @param token 作为限流维度，也可替换为 IP / 用户 ID
     * @return true = 通过；false = 超限
     */
    public boolean allow(String token) {
        // 202504181609 → 精确到分钟
        String minute = DateTimeFormatter.ofPattern("yyyyMMddHHmm")
                                         .format(LocalDateTime.now());
        String key = "rl:" + token + ":" + minute;

        // 原子自增 & 首次设置过期
        Long count = redis.opsForValue().increment(key);
        if (count != null && count == 1) {
            redis.expire(key, WINDOW);
        }
        return count != null && count <= LIMIT;
    }
}
