package cn.telecom.adapter.sf.exception;

import cn.hutool.core.util.StrUtil;
import cn.telecom.adapter.sf.model.RespCode;

/**
 * 自定义异常
 */
public final class CustomException extends RuntimeException {

    private RespCode code;
    private String msg;

    public CustomException() {
        super();
    }

    public CustomException(String message, Throwable cause) {
        super(message, cause);
    }

    public CustomException(String message) {
        super(message);
    }

    public CustomException(Throwable cause) {
        super(cause);
    }

    public CustomException(RespCode code) {
        super();
        this.code = code;
    }

    public CustomException(RespCode code, String msg) {
        super();
        this.code = code;
        this.msg = msg;
    }

    public CustomException(RespCode code, Throwable cause) {
        super(cause);
        this.code = code;
    }

    @Override
    public String getMessage() {
        if(StrUtil.isEmpty(msg)){
            return code.getDesc();
        }else{
            return code.getDesc() + ":" + msg;
        }

    }

    public RespCode getCode() {
        return code;
    }

    public void setCode(RespCode code) {
        this.code = code;
    }
}
