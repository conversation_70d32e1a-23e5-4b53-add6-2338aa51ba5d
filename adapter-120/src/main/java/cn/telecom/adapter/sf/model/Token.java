package cn.telecom.adapter.sf.model;

import java.io.Serializable;

public class Token implements Serializable {

    private static final long serialVersionUID = 1L;
    private String appId;
    private String appKey;
    private String token;

    private long expirationTime;
    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public long getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(long expirationTime) {
        this.expirationTime = expirationTime;
    }
}
