package cn.telecom.adapter.sf.model;

/**
 * 返回类型
 */
public enum RespCode {
    SUCCESS("成功", 0),
    ERROR("系统异常错误", -1),
    INVALID_SIGN("验签未通过", 400001);

    String desc;
    int code;

    RespCode(String desc, int code) {
        this.desc = desc;
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public int getCode() {
        return code;
    }

    @Override
    public String toString() {
        return desc + " , " + code;
    }
}
