package cn.telecom.adapter.sf.service.impl;

import cn.hutool.http.HttpRequest;
import cn.telecom.adapter.sf.service.BindService;
import cn.telecom.common.util.LoggerUtil;
import com.alibaba.fastjson.JSONArray;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
public class BindServiceImpl implements BindService {

    @Value("${custom.appId}")
    private  String appId;
    @Value("${custom.appKey}")
    private String appKey;
    @Value("${custom.customerUrl}")
    private String customerUrl;
    @Value("${custom.addressUrl}")
    private String addressUrl;
    @Override
    public String bind(String phone) {
        //手机号码|0|0|机主名称|0|机主地址
        String result = String.format("%s|0|0|%s|0|%s",phone, getAccName(phone), getAccAddress(phone));
        return result;
    }


    public  String getAccAddress(String accNum) {
        String address = "";
        try{
            HashMap<String, Object> param = new HashMap<>();
            param.put("accNum", accNum);
            param.put("statusCd", "1000");
            param.put("prodUseType", "1000");
            LoggerUtil.info("getAccAddress addressUrl:{}",address);

            String result2 = HttpRequest.get(addressUrl)
                    .header("X-App-Id", appId)
                    .header("X-App-Key", appKey)
                    .form(param)
                    .timeout(20000)//超时，毫秒
                    .execute().body();
//            LoggerUtil.info("getAccAddress result2:{} addressUrl:{}",result2,address);
            JSONArray jsonArray = JSONArray.parseArray(result2);
            if (jsonArray != null && jsonArray.size() > 0) {
                address = jsonArray.getJSONObject(0).getString("addressDesc");
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return address;
    }

    public  String getAccName(String accNum) {
        String custName = "";
        try{
            HashMap<String, Object> param = new HashMap<>();
            param.put("accNum", accNum);
            param.put("queryType", "accNum");
            LoggerUtil.info("getAccAddress customerUrl:{}",customerUrl);
            String result2 = HttpRequest.get(customerUrl)
                    .header("X-App-Id", appId)
                    .header("X-App-Key", appKey)
                    .form(param)
                    .timeout(20000)//超时，毫秒
                    .execute().body();
//            LoggerUtil.info("getAccName result2:{}",result2);
            JSONArray jsonArray = JSONArray.parseArray(result2);

            if (jsonArray != null && jsonArray.size() > 0) {
                custName = jsonArray.getJSONObject(0).getString("custName");
            }
        }catch (Exception e){
            e.printStackTrace();
        }

        return custName;
    }


}
