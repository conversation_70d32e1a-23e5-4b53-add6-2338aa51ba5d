package cn.telecom.adapter.sf.model.resp;

import com.alibaba.fastjson.JSONObject;

import java.io.Serializable;

public class BaseResp implements Serializable {
    private static final long serialVersionUID = 1L;
    private int statusCode;
    private String msg;

    public int getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public void setStatusCodeAndMsg(StatusCode statusCode){
        this.statusCode = statusCode.getCode();
        this.msg=statusCode.getMessage();
    }

    @Override
    public String toString() {
      return JSONObject.toJSONString(this);
    }



}
