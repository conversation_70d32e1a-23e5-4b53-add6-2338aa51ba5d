package cn.telecom.adapter.sf.controller;

import cn.telecom.adapter.sf.service.BindService;
import cn.telecom.adapter.sf.service.EncryptionService;
import cn.telecom.adapter.sf.service.TokenService;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class BindController {
    @Autowired
    BindService bindService;
    @Autowired
    TokenService tokenService;
    @Autowired
    EncryptionService encryptionService;

    @PostMapping("/query")
    public String bind(@RequestHeader("Authorization") String token, @RequestParam("phone") String phone){

        // 验证token
        if (!tokenService.validateToken(token)) {
            return "Invalid token.";
        }

        // 解密手机号
        String decryptedPhone = encryptionService.decrypt(phone);
        return encryptionService.encrypt(bindService.bind(decryptedPhone));
    }



    // 获取token 参数 appid appsecret
    @PostMapping("/token")
    public JSONObject token(@RequestParam("appid") String appid, @RequestParam("appsecret") String appsecret){
        return tokenService.generateToken(appid, appsecret);
    }



    @PostMapping("/encrypt")
    public String encrypt( @RequestParam("phone") String phone){
        return encryptionService.encrypt(phone);
    }
}
