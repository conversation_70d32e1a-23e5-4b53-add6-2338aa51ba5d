package cn.telecom.adapter.sf.model.resp;

public enum StatusCode {

    /**
     * 0	成功	接口通用
     * -1	失败	接口通用
     * 400001	验签未通过	接口通用
     * 890001	X号码不够用	绑定接口
     * 890002	Y号码不够用	绑定接口
     * 890003	A号码中有重复号码	绑定接口、更新接口
     * 890004	A号码组中号码数量不符合要求	绑定接口、更新接口
     * 890005	号码冲突，更新接口中新A号码已经被使用	绑定接口、更新接口
     * 890006	原A号码没有绑定数据	更新接口、解绑接口
     * 890007	原A号码与新A号码相同	更新接口
     * 890008	同一组A号码同时在两个应用中有使用	绑定接口、更新接口
     * 890009	绑定时，指定地市区号无可用的X号码	绑定接口
     * 890010	绑定时，指定地市区号无可用的Y号码	绑定接口
     * 890011	绑定接口失效期时间太短	绑定接口，要大于5分钟
     * 890012	A号码包含异常字符	绑定接口、 更新接口
     * 890013	B号码包含异常字符	绑定接口、 更新接口
     * 890014	invalidTime参数时间格式错误	正确格式：2019-09-25 10:17:18
     * 999999	服务出现未知异常	接口通用
     */
    SUCCESS(0, "成功"),
    FAIL(-1, "失败"),
    SIGNFAIL(400001,"验签未通过"),
    X_NUMBER_LOW(890001,"X号码不够用"),
    Y_NUMBER_LOW(890002,"Y号码不够用"),
    A_NUMBER_REPEAT(890003,"A号码中有重复号码"),
    A_NUMBER_COUNT_FAIL(890004,"A号码组中号码数量不符合要求"),
    A_NUNBER_ALREADY_USED(890005,"号码冲突，更新接口中新A号码已经被使用"),
    A_NUMBER_NOT_BIND(890006,"原A号码没有绑定数据"),
    A_NUMBER_SAME(890007,"原A号码与新A号码相同"),
    A_NUMBERS_IN_ORDER_APP(890008,"同一组A号码同时在两个应用中有使用"),
    CITY_CODE_NOT_HAVE_X_NUMBER(890009,"绑定时，指定地市区号无可用的X号码"),
    INVALID_TIME_TOO_SHORT(890011,"绑定接口失效期时间太短"),
    A_NUMBER_ABNORMAL_CHARACTER(890012,"A号码包含异常字符"),
    B_NUMBER_ABNORMAL_CHARACTER(890013,"B号码包含异常字符"),
    INVALID_TIME_FOMRMAT_ERROR(890014,"invalidTime参数时间格式错误"),
    SYSTEM_ERROR(999999, "服务出现未知异常");

    private int code;

    private String message;

    StatusCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

}