package cn.telecom.adapter.sf.aspect;


import cn.hutool.core.util.StrUtil;
import cn.telecom.adapter.sf.exception.CustomException;
import cn.telecom.adapter.sf.model.RespCode;
import cn.telecom.adapter.sf.model.resp.BaseResp;
import cn.telecom.adapter.sf.model.resp.StatusCode;
import cn.telecom.common.model.App;
import cn.telecom.common.util.LoggerUtil;
import cn.telecom.common.util.redis.RedisKey;
import cn.telecom.common.util.redis.RedisUtil;
import com.alibaba.fastjson.JSONObject;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Component
@Aspect
public class VerifySignAspect {
    @Autowired
    private RedisUtil redisUtil;

    @Pointcut("@annotation(cn.telecom.adapter.sf.annotation.VerifySign)")
    public void pointcut() {
    }

    @Around("pointcut()")
    public Object doInvoke(ProceedingJoinPoint pjp) {
        Object result = null;
        try {
            Object[] args = pjp.getArgs();
            RequestAttributes ra = RequestContextHolder.getRequestAttributes();
            ServletRequestAttributes sra = (ServletRequestAttributes) ra;
            assert sra != null;
            HttpServletRequest request = sra.getRequest();
            verify(request,args[0]);
            result = pjp.proceed();
        } catch (Throwable throwable) {
            LoggerUtil.error(throwable.getMessage(), throwable);
            BaseResp resp = new BaseResp();
            if(throwable instanceof CustomException) {
                CustomException ex = (CustomException) throwable ;
                resp.setMsg(throwable.getMessage());
                resp.setStatusCode(ex.getCode().getCode());
            }else {
                resp.setStatusCodeAndMsg(StatusCode.FAIL);
            }
            result = resp;
        }

        return result;
    }

    private void verify(HttpServletRequest request,Object reqObj){
        String appId = request.getHeader("appId");
        String sign = request.getHeader("sign");
        String ts = request.getHeader("ts");

        if(StrUtil.isEmpty(appId) || StrUtil.isEmpty(sign) || StrUtil.isEmpty(ts)){
            throw new CustomException(RespCode.ERROR,"appId,sign,ts不能为空");
        }
        App app = (App)redisUtil.hget(RedisKey.APP,appId);
        if(app == null){
            throw new CustomException(RespCode.INVALID_SIGN,"appId不存在");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("appId",appId);
        map.put("ts",ts);

        map.putAll(JSONObject.parseObject(reqObj.toString()));
        // 参数按字符顺序排序
        List<String> list = new ArrayList<>(map.keySet());
        list.sort(String::compareTo);
        StringBuilder sb = new StringBuilder();
        sb.append(app.getAppSecret());
        list.forEach(k -> {
            sb.append(k).append(map.get(k));
        });
        String psign = DigestUtils.md5DigestAsHex(sb.toString().getBytes()).toUpperCase(Locale.ROOT);
        if(!psign.equals(sign)){
            throw new CustomException(RespCode.INVALID_SIGN);
        }
    }

    public static void main(String[] args) {
        Map<String, Object> map = new HashMap<>();
        map.put("appId","9Wm5XfEL");
        String ts = String.valueOf(System.currentTimeMillis());
        System.out.println(ts);
        map.put("ts",ts);
        map.put("empId","11111");
        map.put("cityCode","021");
        map.put("customerPhone","15100000000");
        map.put("courierPhone","15200000000");
        map.put("invalidTime","2021-10-11 12:00:00");
        map.put("sysCode","test");
        List<String> list = new ArrayList<>(map.keySet());
        list.sort(String::compareTo);
        StringBuilder sb = new StringBuilder();
        sb.append("fa914ee98424c91f0b4851a1371aa2eefe55349a");
        list.forEach(k -> {
            sb.append(k).append(map.get(k));
        });
        System.out.println(sb.toString());
        String sign = DigestUtils.md5DigestAsHex(sb.toString().getBytes()).toUpperCase(Locale.ROOT);
        System.out.println(sign);
    }
}
