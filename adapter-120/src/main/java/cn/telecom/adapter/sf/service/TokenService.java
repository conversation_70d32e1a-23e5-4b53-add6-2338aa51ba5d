package cn.telecom.adapter.sf.service;

import cn.telecom.adapter.sf.model.Token;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Service
public class TokenService {
    @Value("${app.credentials.appId}")
    private String validAppId;

    @Value("${app.credentials.appSecret}")
    private String validAppSecret;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    public JSONObject generateToken(String appId, String appKey) {

        if (!validateAppCredentials(appId, appKey)) {
            // 如果 appId 和 appSecret 无效，返回 null 或抛出异常
            return null;
        }
        Token token = new Token();
        token.setAppId(appId);
        token.setAppKey(appKey);
        token.setToken(appId + "-" + UUID.randomUUID().toString());

        // 将 token 存入 Redis，并设置过期时间为 7200 秒
        redisTemplate.opsForValue().set(token.getAppId(), token.getToken(), 7200, TimeUnit.SECONDS);

        // 返回 token和过期时间
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("token", token.getToken());
        jsonObject.put("expire", 7200);

        return jsonObject;
    }
    public boolean validateAppCredentials(String appId, String appSecret) {
        return validAppId.equals(appId) && validAppSecret.equals(appSecret);
    }
    public boolean validateToken(String token) {
        // 在Redis中查找token
        String appId = getAppIdFromToken(token);

        if (appId != null && !appId.isEmpty()) {
            String storedToken = redisTemplate.opsForValue().get(appId);
            return token.equals(storedToken);
        }
        return false;
    }

    private String getAppIdFromToken(String token) {
        if (token == null || token.isEmpty()) {
            return null;
        }

        // 拆分 token，提取 appId
        String[] parts = token.split("-", 2);
        if (parts.length != 2) {
            return null;
        }

        return parts[0];
    }

}
